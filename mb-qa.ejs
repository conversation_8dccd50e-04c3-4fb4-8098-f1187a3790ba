{
    "port": "8080",
    "protocol": "http",
    "name": "atimi-mock",
    "useCORS": true,
    "defaultResponse": {
        "headers": {
          "Content-Type": "application/json",
          "Access-Control-Allow-Headers": "*",
          "Access-Control-Allow-Methods": "*",
          "Access-Control-Allow-Origin": "*"
        }
      },
    "stubs": [
        <% include ./eapi/eapi-service-qa.ejs %>,
        <% include ./coop/coop-service-qa.ejs %>
    ]
}