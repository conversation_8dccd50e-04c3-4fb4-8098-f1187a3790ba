{"predicates": [{"equals": {"method": "POST", "path": "/v1/party/cust-profile/custinfo/findByAccount"}}, {"contains": {"body": {"accountNumber": "**********"}}}], "responses": [{"is": {"statusCode": 500, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "1234", "originalErrorDesc": "Credit Card Not found", "description": "The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.", "message": "Missing juristicId query parameter", "moreInfo": "ORIGINAL ERROR: E1234 MISSING JURISTIC-ID", "severitylevel": "Error", "originalErrorCode": "RM7885"}]}}, "_behaviors": {"wait": 100}}]}