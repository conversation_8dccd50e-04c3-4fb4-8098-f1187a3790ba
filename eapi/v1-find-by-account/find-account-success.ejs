{"predicates": [{"equals": {"method": "POST", "path": "/v1/party/cust-profile/custinfo/findByAccount"}}, {"contains": {"body": {"accountNumber": "**********"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"pagination": {"pagingLimit": 20, "pagingOffset": "1", "previousPage": "", "firstPage": "", "lastPage": "", "nextPage": ""}, "items": [{"customerType": "C", "engName": "MOCK FINDBYACCOUNT", "thaiName": " ", "primaryAccountOwnerFlag": true, "partnerID": "********", "idNumber": "*************", "vipCode": "CE", "idTypeCode": "B8"}]}}, "_behaviors": {"wait": 100}}]}