{"predicates": [{"equals": {"method": "POST", "path": "/v1/payment/cardless/withdraw/confirm"}}, {"contains": {"body": {"CardlessCashRq": {"MobileNo": "0810000504"}}}}], "responses": [{"is": {"statusCode": 504, "headers": {"Content-Type": "application/json"}, "body": {"code": "JMS01", "description": "JMS Message timeout, No response return within limit (default 100 second).", "message": "JMS Timeout", "moreInfo": ".", "errors": [{"description": "JMS Message timeout, No response return within limit (default 100 second).", "code": "JMS01", "message": "JMS Timeout", "moreInfo": "JMS Message timeout, Please contact the SCB API team for support.", "errors": "", "severitylevel": "ERROR"}], "severitylevel": "ERROR"}, "_behaviors": {"wait": 100}}}]}