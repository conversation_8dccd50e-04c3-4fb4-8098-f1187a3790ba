{"predicates": [{"equals": {"method": "POST", "path": "/v1/payment/cardless/withdraw/initiate"}}, {"contains": {"body": {"CardlessCashRq": {"MobileNo": "0819999999"}}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"CardlessCashRs": {"RqUID": "59b01000-96bb-4f2d-82d7-f166a4372ffe", "Status": {"StatusCode": "00", "StatusDesc": "Success"}, "TranInfo": {"MsgType": "0710", "TermFiid": "SCB", "TerminalId": "012345678901234", "TranCode": "301800", "Sequence": "000001", "Date": "170429", "Time": "112355"}, "TranAmount": 1000.0, "MobileNo": "0865123369", "BranchId": "3245", "AcctId": "384995886", "CasaAmt": 200.1, "SecondaryAmt": 100.1, "ServicePurpose": "Happy Cash", "CardRefNo": "1234567890123456", "ThirdPartyCashWithdrawalFeeAmount": 12.35, "ThirdPartyCashWithdrawalFeeCurrency": "THB", "PaymentType": "CC_CASA_SKY", "ThirdPartyCashWithdrawalTransactionCount": 4, "Rmid": "1234567"}}, "_behaviors": {"wait": 100}}}]}