{"predicates": [{"equals": {"method": "POST", "path": "/v1/accounts/deposits/debitCredit/verify"}}, {"contains": {"body": {"DebitCreditVerifyRq": {"DepAccTo": {"AccNo": "**********"}}}}}], "responses": [{"is": {"statusCode": 500, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "LG0003", "message": "Business rule error from legacy", "severity": "ERROR", "description": "There are some errors from legacy : {legacy endpoint}", "moreInfo": "", "originalError": {"responseStatus": "", "responseCode": "{StatusCode}", "responseCodeDescription": "{StatusDesc}", "extStatusDesc": ""}}]}, "_behaviors": {"wait": 100}}}]}