{"predicates": [{"equals": {"method": "POST", "path": "/v1/accounts/deposits/debitCredit/verify"}}, {"contains": {"body": {"DebitCreditVerifyRq": {"HostParams": {"TransDesc": "CREDITVERIFY"}, "DepAccFrom": {"AccNo": "**********"}, "DepAccTo": {"AccNo": "**********"}}}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"DebitCreditVerifyRs": {"Status": {"Control": "0", "StatusCode": "MIM050", "StatusDesc": "Transaction successful"}, "RqUID": "REQ1234567890ABCDEF1234567890ABCD", "TaskNo": "000123", "PrcDate": "********", "PrcTime": "183528", "DepAccFrom": {"AccNo": "**********", "AccShortName": "ทดสอบชื่อสั้น", "AccName": "ทดสอบชื่อ นามสกุล", "AccBalance": "+100000.00", "AccAvailableBalance": "+95000.00", "OpenDate": "********", "LastTrnDate": "********", "OdAmount": "0.00", "HoldAmount": "5000.00", "TodayHoldAmount": "1000.00", "YesterdayAmount": "2000.00", "UnclearAmount": "0.00", "TodayCashAmount": "3000.00", "OpenDepAmount": "100000.00", "OCCode": "OC1234567890", "CurrencyCode": "THB", "AccStatusCode": "ACT", "CustType": "INDIVIDUAL", "MiscMsg": "No issues", "AccProductCode": "SAV001", "BranchId": "BR1234567890", "AccType": "Savings", "BranchRegion": "Bangkok", "BranchName": "Chatuchak Branch"}, "DepAccTo": {"AccNo": "**********", "AccShortName": "RECEIVER", "AccName": "RECEIVER NAME", "AccBalance": "+50000.00", "AccAvailableBalance": "+48000.00", "OpenDate": "********", "LastTrnDate": "********", "Note": "SCB Enterprise API Implementation by Enterprise Architecture Siam Commercial Bank Public Company Limited Error Handling Failure case for MQ EAPI Handle response when Bad Request such as mandatory field validation, response: HTTP 400 Unauthorized, response: HTTP401 Internal server error occurred, response: HTTP 500 Timeout: HTTP 504 Follow the backend response when MQ return \"<DocRs><DebitCreditVerifyRs><Status><Control/>\" value is \"1\" (Failed). as below", "OdAmount": "0.00", "HoldAmount": "2000.00", "TodayHoldAmount": "500.00", "YesterdayAmount": "1000.00", "UnclearAmount": "0.00", "TodayCashAmount": "1500.00", "OpenDepAmount": "50000.00", "OCCode": "OC0987654321", "CurrencyCode": "THB", "AccStatusCode": "ACT", "CustType": "INDIVIDUAL", "MiscMsg": "No issues", "AccProductCode": "SAV002", "BranchId": "BR0987654321", "AccType": "Savings", "BranchRegion": "<PERSON>", "BranchName": "Nimman Branch"}}}}}]}