{"predicates": [{"equals": {"method": "POST", "path": "/v3/customerService/alerts"}}, {"contains": {"body": {"DestinationAddr": "0838888888"}}}], "responses": [{"is": {"statusCode": 500, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"description": "REQUEST BODY IS INVALID", "code": "S0001", "message": "REQUEST BODY IS INVALID", "moreInfo": "Check request body for invalid input.", "severitylevel": "Error"}]}, "_behaviors": {"wait": 100}}}]}