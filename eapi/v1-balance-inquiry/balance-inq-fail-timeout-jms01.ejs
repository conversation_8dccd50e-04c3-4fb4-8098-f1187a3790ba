{"predicates": [{"equals": {"method": "POST", "path": "/v1/accounts/deposits/balanceInquiry"}}, {"contains": {"body": {"accountNumbers": ["**********"]}}}], "responses": [{"is": {"statusCode": 504, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "JMS01", "message": "JMS Timeout", "severitylevel": "Error", "description": "JMS Message timeout, No response return within limit (default 100 second).", "moreInfo": "JMS01: Please contact the SCB API team for support."}]}}, "_behaviors": {"wait": 100}}]}