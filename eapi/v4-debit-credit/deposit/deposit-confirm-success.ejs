{"predicates": [{"equals": {"method": "POST", "path": "/v4/accounts/deposits/debitCreditService"}}, {"contains": {"body": {"toAccount": {"accountNumber": "**********"}, "transactionType": "CREDIT"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"toAccount": {"accountShortName": "SOMCHAI S.", "accountType": "32", "accountLongName": "SOMCHAI SATANG", "accountCurrency": "764", "balanceInfo": {"accountBalances": [{"amount": 150000.99, "balanceType": "BALANCE"}], "extendedAccountBalances": [{"amount": 150000.99, "balanceType": "FIXEDLOANAMOUNT"}], "accountInterest": [{"interestType": "ACCUINTER", "amount": 150000.99}]}, "accountNumber": "**********", "accountBranch": {"branchCode": "0111", "branchRegionCode": "001", "branchName": "SATHAYOTIN BUREAU", "bankID": "14"}, "businessMissionCode": "3720"}, "requestUID": "25505f86a2e058cf41d5cf8abf87", "processingDate": "2017-06-23", "fromAccount": {"accountShortName": "SOMCHAI S.", "accountType": "32", "accountLongName": "SOMCHAI SATANG", "accountCurrency": "764", "balanceInfo": {"accountBalances": [{"amount": 150000.99, "balanceType": "BALANCE"}], "extendedAccountBalances": [{"amount": 150000.99, "balanceType": "FIXEDLOANAMOUNT"}], "accountInterest": [{"interestType": "ACCUINTER", "amount": 150000.99}]}, "accountNumber": "**********", "accountBranch": {"branchCode": "0111", "branchRegionCode": "001", "branchName": "SATHAYOTIN BUREAU", "bankID": "14"}, "businessMissionCode": "3720"}, "originalRequestUID": "25505f86a2e058cf41d9a19647", "processingTime": "10:06:20"}}, "_behaviors": {"wait": 100}}]}