{"predicates": [{"equals": {"method": "POST", "path": "/v4/accounts/deposits/debitCreditService"}}, {"contains": {"body": {"toAccount": {"accountNumber": "**********"}, "transactionType": "CREDIT"}}}], "responses": [{"is": {"statusCode": 409, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "BU0001", "message": "Data not found", "severitylevel": "ERROR", "description": "Data not found", "moreInfo": "1: 23;TRANSACTION INCOMPLETED;ERROR MSG. FROM HOSTDSFRLOG :EIB 0000084"}]}}, "_behaviors": {"wait": 100}}]}