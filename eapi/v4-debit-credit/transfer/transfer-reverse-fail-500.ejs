{"predicates": [{"equals": {"method": "POST", "path": "/v4/accounts/deposits/debitCreditService"}}, {"contains": {"body": {"fromAccount": {"accountNumber": "**********"}, "toAccount": {"accountNumber": "**********"}, "transactionType": "REVTRANSFER"}}}], "responses": [{"is": {"statusCode": 500, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "1234", "originalErrorDesc": "Credit Card Not found", "description": "The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.", "message": "Missing juristicId query parameter", "moreInfo": "", "severitylevel": "Error", "originalErrorCode": "RM7885"}]}}}]}