{"predicates": [{"equals": {"method": "POST", "path": "/v4/accounts/deposits/debitCreditService"}}, {"contains": {"body": {"fromAccount": {"accountNumber": "**********"}, "remittanceInfo": {"fees": [{"feeType": "TRAN"}]}, "transactionType": "DEBIT"}}}], "responses": [{"is": {"statusCode": 504, "headers": {"Content-Type": "application/json"}, "body": {"code": "JMS01", "description": "JMS Message timeout, No response return within limit (default 100 second).", "message": "JMS Timeout", "moreInfo": ".", "errors": [{"description": "JMS Message timeout, No response return within limit (default 100 second).", "code": "JMS01", "message": "JMS Timeout", "moreInfo": "JMS Message timeout, Please contact the SCB API team for support.", "errors": "", "severitylevel": "ERROR"}], "severitylevel": "ERROR"}}}]}