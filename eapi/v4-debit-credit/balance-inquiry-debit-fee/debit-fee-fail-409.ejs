{"predicates": [{"equals": {"method": "POST", "path": "/v4/accounts/deposits/debitCreditService"}}, {"equals": {"body": {"fromAccount": {"accountNumber": "**********"}, "remittanceInfo": {"fees": [{"feeType": "TRAN"}]}, "transactionType": "DEBIT"}}}], "responses": [{"is": {"statusCode": 409, "headers": {"Content-Type": "application/json"}, "body": {"errors": [{"code": "BU0001", "message": "Data not found", "severitylevel": "ERROR", "description": "Data not found", "moreInfo": "1: 23;TRANSACTION INCOMPLETED;ERROR MSG. FROM HOSTDSFRLOG :EIB 0000084"}]}}}]}