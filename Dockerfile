FROM harbordev.se.scb.co.th/library/cc/common:base AS common

# -- Build stage --
FROM node:20-alpine3.21 AS builder

# Set working directory
WORKDIR /app

# Install Mountebank
RUN npm install -g mountebank@2.9.1

# Copy all files to working directory
COPY . .

# Expose the port that the mock service runs on
EXPOSE 8080

# Command to run the Mountebank server
CMD ["mb", "--configfile", "/app/mock.ejs", "--allowInjection"]