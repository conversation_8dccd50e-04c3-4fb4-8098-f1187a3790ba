{"level":"warn","message":"[mb:2525] Running with --allowInjection set. See http://localhost:2525/docs/security for security info","timestamp":"2025-09-17T12:08:45.959Z"}
{"level":"info","message":"[mb:2525] mountebank v2.9.1 now taking orders - point your browser to http://localhost:2525/ for help","timestamp":"2025-09-17T12:08:45.961Z"}
{"level":"debug","message":"[mb:2525] config: {\"options\":{\"configfile\":\"mock.ejs\",\"allowInjection\":true,\"port\":2525,\"noParse\":false,\"formatter\":\"mountebank-formatters\",\"pidfile\":\"mb.pid\",\"log\":{\"level\":\"debug\",\"transports\":{\"console\":{\"colorize\":true,\"format\":\"%level: %message\"},\"file\":{\"path\":\"mb.log\",\"format\":\"json\"}}},\"localOnly\":false,\"ipWhitelist\":[\"*\"],\"mock\":false,\"debug\":false,\"protofile\":\"protocols.json\",\"origin\":false,\"apikey\":null},\"process\":{\"nodeVersion\":\"v21.6.1\",\"architecture\":\"arm64\",\"platform\":\"darwin\"}}","timestamp":"2025-09-17T12:08:45.961Z"}
{"level":"info","message":"[mb:2525] PUT /imposters","timestamp":"2025-09-17T12:08:46.012Z"}
{"level":"debug","message":"[mb:2525] ::1:56864 => {\"imposters\":[{\"port\":\"8080\",\"protocol\":\"http\",\"name\":\"atimi-mock\",\"useCORS\":true,\"defaultResponse\":{\"headers\":{\"Content-Type\":\"application/json\",\"Access-Control-Allow-Headers\":\"*\",\"Access-Control-Allow-Methods\":\"*\",\"Access-Control-Allow-Origin\":\"*\"}},\"stubs\":[{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"BU0001\",\"message\":\"Data not found\",\"severitylevel\":\"ERROR\",\"description\":\"Data not found\",\"moreInfo\":\"1: 23;TRANSACTION INCOMPLETED;ERROR MSG. FROM HOSTDSFRLOG :EIB 0000084\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"equals\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"REVDEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"remittanceInfo\":{\"fees\":[{\"feeType\":\"TRAN\"}]},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"equals\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"remittanceInfo\":{\"fees\":[{\"feeType\":\"TRAN\"}]},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"BU0001\",\"message\":\"Data not found\",\"severitylevel\":\"ERROR\",\"description\":\"Data not found\",\"moreInfo\":\"1: 23;TRANSACTION INCOMPLETED;ERROR MSG. FROM HOSTDSFRLOG :EIB 0000084\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"remittanceInfo\":{\"fees\":[{\"feeType\":\"TRAN\"}]},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"1234\",\"originalErrorDesc\":\"Credit Card Not found\",\"description\":\"The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.\",\"message\":\"Missing juristicId query parameter\",\"moreInfo\":\"ORIGINAL ERROR: E1234 MISSING JURISTIC-ID\",\"severitylevel\":\"Error\",\"originalErrorCode\":\"RM7885\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"remittanceInfo\":{\"fees\":[{\"feeType\":\"TRAN\"}]},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"remittanceInfo\":{\"fees\":[{\"feeType\":\"TRAN\"}]},\"transactionType\":\"DEBIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"CREDIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"CREDIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"BU0001\",\"message\":\"Data not found\",\"severitylevel\":\"ERROR\",\"description\":\"Data not found\",\"moreInfo\":\"1: 23;TRANSACTION INCOMPLETED;ERROR MSG. FROM HOSTDSFRLOG :EIB 0000084\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"CREDIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"CREDIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"CREDIT\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"TRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"TRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"TRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"TRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"1234\",\"originalErrorDesc\":\"Credit Card Not found\",\"description\":\"The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.\",\"message\":\"Missing juristicId query parameter\",\"moreInfo\":\"\",\"severitylevel\":\"Error\",\"originalErrorCode\":\"RM7885\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"REVTRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"toAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"requestUID\":\"25505f86a2e058cf41d5cf8abf87\",\"processingDate\":\"2017-06-23\",\"fromAccount\":{\"accountShortName\":\"SOMCHAI S.\",\"accountType\":\"32\",\"accountLongName\":\"SOMCHAI SATANG\",\"accountCurrency\":\"764\",\"balanceInfo\":{\"accountBalances\":[{\"amount\":150000.99,\"balanceType\":\"BALANCE\"}],\"extendedAccountBalances\":[{\"amount\":150000.99,\"balanceType\":\"FIXEDLOANAMOUNT\"}],\"accountInterest\":[{\"interestType\":\"ACCUINTER\",\"amount\":150000.99}]},\"accountNumber\":\"**********\",\"accountBranch\":{\"branchCode\":\"0111\",\"branchRegionCode\":\"001\",\"branchName\":\"SATHAYOTIN BUREAU\",\"bankID\":\"14\"},\"businessMissionCode\":\"3720\"},\"originalRequestUID\":\"25505f86a2e058cf41d9a19647\",\"processingTime\":\"10:06:20\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"REVTRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"1234\",\"originalErrorDesc\":\"Credit Card Not found\",\"description\":\"The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.\",\"message\":\"Missing juristicId query parameter\",\"moreInfo\":\"\",\"severitylevel\":\"Error\",\"originalErrorCode\":\"RM7885\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/accounts/deposits/debitCreditService\"}},{\"contains\":{\"body\":{\"fromAccount\":{\"accountNumber\":\"**********\"},\"toAccount\":{\"accountNumber\":\"**********\"},\"transactionType\":\"REVTRANSFER\"}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v3/customerService/alerts\"}},{\"contains\":{\"body\":{\"DestinationAddr\":\"**********\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v3/customerService/alerts\"}},{\"contains\":{\"body\":{\"DestinationAddr\":\"**********\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"description\":\"REQUEST BODY IS INVALID\",\"code\":\"S0001\",\"message\":\"REQUEST BODY IS INVALID\",\"moreInfo\":\"Check request body for invalid input.\",\"severitylevel\":\"Error\"}]},\"_behaviors\":{\"wait\":100}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/payment/cardless/withdraw/confirm\"}},{\"contains\":{\"body\":{\"CardlessCashRq\":{\"MobileNo\":\"0810000504\"}}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"},\"_behaviors\":{\"wait\":100}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/payment/cardless/withdraw/confirm\"}},{\"contains\":{\"body\":{\"CardlessCashRq\":{\"MobileNo\":\"0819999999\"}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"CardlessCashRs\":{\"RqUID\":\"59b01000-96bb-4f2d-82d7-f166a4372ffe\",\"Status\":{\"StatusCode\":\"00\",\"StatusDesc\":\"Success\"},\"TranInfo\":{\"MsgType\":\"0710\",\"TerminalId\":\"01234**********\",\"TranCode\":\"301800\",\"Sequence\":\"000001\",\"Date\":\"170429\",\"Time\":\"112355\"},\"TranAmount\":1000,\"MobileNo\":\"**********\",\"BranchId\":\"3245\",\"AcctId\":\"384995886\",\"CasaAmt\":200.1,\"SecondaryAmt\":100.1,\"ServicePurpose\":\"Happy Cash\",\"CardRefNo\":\"****************\",\"ThirdPartyCashWithdrawalFeeAmount\":12.35,\"ThirdPartyCashWithdrawalFeeCurrency\":\"THB\",\"PaymentType\":\"CC_CASA_SKY\",\"ThirdPartyCashWithdrawalTransactionCount\":4,\"Rmid\":\"1234567\"},\"_behaviors\":{\"wait\":100}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/payment/cardless/withdraw/initiate\"}},{\"contains\":{\"body\":{\"CardlessCashRq\":{\"MobileNo\":\"0819999999\"}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"CardlessCashRs\":{\"RqUID\":\"59b01000-96bb-4f2d-82d7-f166a4372ffe\",\"Status\":{\"StatusCode\":\"00\",\"StatusDesc\":\"Success\"},\"TranInfo\":{\"MsgType\":\"0710\",\"TermFiid\":\"SCB\",\"TerminalId\":\"01234**********\",\"TranCode\":\"301800\",\"Sequence\":\"000001\",\"Date\":\"170429\",\"Time\":\"112355\"},\"TranAmount\":1000,\"MobileNo\":\"**********\",\"BranchId\":\"3245\",\"AcctId\":\"384995886\",\"CasaAmt\":200.1,\"SecondaryAmt\":100.1,\"ServicePurpose\":\"Happy Cash\",\"CardRefNo\":\"****************\",\"ThirdPartyCashWithdrawalFeeAmount\":12.35,\"ThirdPartyCashWithdrawalFeeCurrency\":\"THB\",\"PaymentType\":\"CC_CASA_SKY\",\"ThirdPartyCashWithdrawalTransactionCount\":4,\"Rmid\":\"1234567\"}},\"_behaviors\":{\"wait\":100}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/payment/cardless/withdraw/reverse\"}},{\"contains\":{\"body\":{\"CardlessCashRq\":{\"MobileNo\":\"0810000504\"}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"CardlessCashRs\":{\"RqUID\":\"59b01000-96bb-4f2d-82d7-f166a4372ffe\",\"Status\":{\"statusCode\":\"00\",\"statusDesc\":\"Success\"},\"TranInfo\":{\"MsgType\":\"0710\",\"TerminalId\":\"01234**********\",\"TranCode\":\"301800\",\"Sequence\":\"000001\",\"Date\":\"170429\",\"Time\":\"112355\"},\"TranAmount\":1000,\"MobileNo\":\"**********\",\"CasaAmt\":200.1,\"SecondaryAmt\":100.1,\"ServicePurpose\":\"Happy Cash\",\"CardRefNo\":\"****************\",\"ThirdPartyCashWithdrawalFeeAmount\":12.35,\"ThirdPartyCashWithdrawalFeeCurrency\":\"THB\",\"PaymentType\":\"CARDLESS_REVERSAL\",\"Rmid\":\"1234567\"},\"_behaviors\":{\"wait\":100}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/balanceInquiry\"}},{\"contains\":{\"body\":{\"accountNumbers\":[\"**********\"]}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/balanceInquiry\"}},{\"contains\":{\"body\":{\"accountNumbers\":[\"**********\"]}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"severitylevel\":\"Error\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"moreInfo\":\"JMS01: Please contact the SCB API team for support.\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/balanceInquiry\"}},{\"contains\":{\"body\":{\"accountNumbers\":[\"**********\"]}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"FW000\",\"message\":\"GENERAL API ERROR\",\"severitylevel\":\"ERROR\",\"description\":\"GENERAL API ERROR\",\"moreInfo\":\"\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/balanceInquiry\"}},{\"contains\":{\"body\":{\"accountNumbers\":[\"**********\"]}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"items\":[{\"accountNumber\":\"**********\",\"branchId\":\"0813\",\"accountType\":\"2\",\"accountName\":\"NCBSI QA\",\"branchRegion\":\"054\",\"branchName\":\"THE MALL NAKHORNRACHASIMA B\",\"accountOC\":\"3261\",\"bankAccountStatusCode\":\"OPEN\",\"drcrAllowance\":\"\",\"onlineOpenFlag\":\"BU\",\"productCode\":\"103\",\"accountBalanceInfo\":[{\"balanceType\":\"BALANCE\",\"amount\":\"+11,535.32\",\"currencyCovertRule\":\"DIRECT\",\"currencyCode\":\"764\",\"currencyRate\":\"***********\"},{\"balanceType\":\"AVAILBALANCE\",\"amount\":\"+11,535.32\",\"currencyCovertRule\":\"DIRECT\",\"currencyCode\":\"764\",\"currencyRate\":\"***********\"}]}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/balanceInquiry\"}},{\"or\":[{\"contains\":{\"body\":{\"accountNumbers\":[\"**********\"]}}},{\"equals\":{\"body\":{\"accountNumbers\":[\"**********\"]}}},{\"equals\":{\"body\":{\"accountNumbers\":[\"**********\"]}}},{\"equals\":{\"body\":{\"accountNumbers\":[\"**********\"]}}},{\"equals\":{\"body\":{\"accountNumbers\":[\"**********\"]}}}]}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"items\":[{\"accountNumber\":\"**********\",\"branchId\":\"0813\",\"accountType\":\"2\",\"accountName\":\"NCBSI QA\",\"branchRegion\":\"054\",\"branchName\":\"THE MALL NAKHORNRACHASIMA B\",\"accountOC\":\"3261\",\"bankAccountStatusCode\":\"OPEN\",\"drcrAllowance\":\"\",\"onlineOpenFlag\":\"BU\",\"productCode\":\"103\",\"accountBalanceInfo\":[{\"balanceType\":\"BALANCE\",\"amount\":\"+11,535.32\",\"currencyCovertRule\":\"DIRECT\",\"currencyCode\":\"764\",\"currencyRate\":\"***********\"},{\"balanceType\":\"AVAILBALANCE\",\"amount\":\"+11,535.32\",\"currencyCovertRule\":\"DIRECT\",\"currencyCode\":\"764\",\"currencyRate\":\"***********\"}]}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"LG0003\",\"message\":\"Business rule error from legacy\",\"severity\":\"ERROR\",\"description\":\"There are some errors from legacy : {legacy endpoint}\",\"moreInfo\":\"\",\"originalError\":{\"responseStatus\":\"\",\"responseCode\":\"{StatusCode}\",\"responseCodeDescription\":\"{StatusDesc}\",\"extStatusDesc\":\"\"}}]},\"_behaviors\":{\"wait\":100}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DebitCreditVerifyRs\":{\"Status\":{\"Control\":\"0\",\"StatusCode\":\"MIM050\",\"StatusDesc\":\"Transaction successful\"},\"RqUID\":\"REQ1234567890ABCDEF1234567890ABCD\",\"TaskNo\":\"000123\",\"PrcDate\":\"********\",\"PrcTime\":\"183528\",\"DepAccFrom\":{\"AccNo\":\"**********\",\"AccShortName\":\"ทดสอบชื่อสั้น\",\"AccName\":\"ทดสอบชื่อ นามสกุล\",\"AccBalance\":\"+100000.00\",\"AccAvailableBalance\":\"+95000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"5000.00\",\"TodayHoldAmount\":\"1000.00\",\"YesterdayAmount\":\"2000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"3000.00\",\"OpenDepAmount\":\"100000.00\",\"OCCode\":\"OC1234567890\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV001\",\"BranchId\":\"BR1234567890\",\"AccType\":\"Savings\",\"BranchRegion\":\"Bangkok\",\"BranchName\":\"Chatuchak Branch\"},\"DepAccTo\":{\"AccNo\":\"**********\",\"AccShortName\":\"RECEIVER\",\"AccName\":\"RECEIVER NAME\",\"AccBalance\":\"+50000.00\",\"AccAvailableBalance\":\"+48000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"Note\":\"SCB Enterprise API Implementation by Enterprise Architecture Siam Commercial Bank Public Company Limited Error Handling Failure case for MQ EAPI Handle response when Bad Request such as mandatory field validation, response: HTTP 400 Unauthorized, response: HTTP401 Internal server error occurred, response: HTTP 500 Timeout: HTTP 504 Follow the backend response when MQ return \\\"<DocRs><DebitCreditVerifyRs><Status><Control/>\\\" value is \\\"1\\\" (Failed). as below\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"2000.00\",\"TodayHoldAmount\":\"500.00\",\"YesterdayAmount\":\"1000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"1500.00\",\"OpenDepAmount\":\"50000.00\",\"OCCode\":\"OC0987654321\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV002\",\"BranchId\":\"BR0987654321\",\"AccType\":\"Savings\",\"BranchRegion\":\"Chiang Mai\",\"BranchName\":\"Nimman Branch\"}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"DEBITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DebitCreditVerifyRs\":{\"Status\":{\"Control\":\"0\",\"StatusCode\":\"MIM050\",\"StatusDesc\":\"Transaction successful\"},\"RqUID\":\"REQ1234567890ABCDEF1234567890ABCD\",\"TaskNo\":\"000123\",\"PrcDate\":\"********\",\"PrcTime\":\"183528\",\"DepAccFrom\":{\"AccNo\":\"**********\",\"AccShortName\":\"ทดสอบชื่อสั้น\",\"AccName\":\"ทดสอบชื่อ นามสกุล\",\"AccBalance\":\"+100000.00\",\"AccAvailableBalance\":\"+95000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"5000.00\",\"TodayHoldAmount\":\"1000.00\",\"YesterdayAmount\":\"2000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"3000.00\",\"OpenDepAmount\":\"100000.00\",\"OCCode\":\"OC1234567890\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV001\",\"BranchId\":\"BR1234567890\",\"AccType\":\"Savings\",\"BranchRegion\":\"Bangkok\",\"BranchName\":\"Chatuchak Branch\"},\"DepAccTo\":{\"AccNo\":\"**********\",\"AccShortName\":\"RECEIVER\",\"AccName\":\"RECEIVER NAME\",\"AccBalance\":\"+50000.00\",\"AccAvailableBalance\":\"+48000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"Note\":\"SCB Enterprise API Implementation by Enterprise Architecture Siam Commercial Bank Public Company Limited Error Handling Failure case for MQ EAPI Handle response when Bad Request such as mandatory field validation, response: HTTP 400 Unauthorized, response: HTTP401 Internal server error occurred, response: HTTP 500 Timeout: HTTP 504 Follow the backend response when MQ return \\\"<DocRs><DebitCreditVerifyRs><Status><Control/>\\\" value is \\\"1\\\" (Failed). as below\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"2000.00\",\"TodayHoldAmount\":\"500.00\",\"YesterdayAmount\":\"1000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"1500.00\",\"OpenDepAmount\":\"50000.00\",\"OCCode\":\"OC0987654321\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV002\",\"BranchId\":\"BR0987654321\",\"AccType\":\"Savings\",\"BranchRegion\":\"Chiang Mai\",\"BranchName\":\"Nimman Branch\"}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"DEBITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"FW000\",\"message\":\"GENERAL API ERROR\",\"severitylevel\":\"ERROR\",\"description\":\"GENERAL API ERROR\",\"moreInfo\":\"\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"DEBITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"DEBITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"CREDITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DebitCreditVerifyRs\":{\"Status\":{\"Control\":\"0\",\"StatusCode\":\"MIM050\",\"StatusDesc\":\"Transaction successful\"},\"RqUID\":\"REQ1234567890ABCDEF1234567890ABCD\",\"TaskNo\":\"000123\",\"PrcDate\":\"********\",\"PrcTime\":\"183528\",\"DepAccFrom\":{\"AccNo\":\"**********\",\"AccShortName\":\"ทดสอบชื่อสั้น\",\"AccName\":\"ทดสอบชื่อ นามสกุล\",\"AccBalance\":\"+100000.00\",\"AccAvailableBalance\":\"+95000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"5000.00\",\"TodayHoldAmount\":\"1000.00\",\"YesterdayAmount\":\"2000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"3000.00\",\"OpenDepAmount\":\"100000.00\",\"OCCode\":\"OC1234567890\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV001\",\"BranchId\":\"BR1234567890\",\"AccType\":\"Savings\",\"BranchRegion\":\"Bangkok\",\"BranchName\":\"Chatuchak Branch\"},\"DepAccTo\":{\"AccNo\":\"**********\",\"AccShortName\":\"RECEIVER\",\"AccName\":\"RECEIVER NAME\",\"AccBalance\":\"+50000.00\",\"AccAvailableBalance\":\"+48000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"Note\":\"SCB Enterprise API Implementation by Enterprise Architecture Siam Commercial Bank Public Company Limited Error Handling Failure case for MQ EAPI Handle response when Bad Request such as mandatory field validation, response: HTTP 400 Unauthorized, response: HTTP401 Internal server error occurred, response: HTTP 500 Timeout: HTTP 504 Follow the backend response when MQ return \\\"<DocRs><DebitCreditVerifyRs><Status><Control/>\\\" value is \\\"1\\\" (Failed). as below\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"2000.00\",\"TodayHoldAmount\":\"500.00\",\"YesterdayAmount\":\"1000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"1500.00\",\"OpenDepAmount\":\"50000.00\",\"OCCode\":\"OC0987654321\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV002\",\"BranchId\":\"BR0987654321\",\"AccType\":\"Savings\",\"BranchRegion\":\"Chiang Mai\",\"BranchName\":\"Nimman Branch\"}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"CREDITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DebitCreditVerifyRs\":{\"Status\":{\"Control\":\"0\",\"StatusCode\":\"MIM050\",\"StatusDesc\":\"Transaction successful\"},\"RqUID\":\"REQ1234567890ABCDEF1234567890ABCD\",\"TaskNo\":\"000123\",\"PrcDate\":\"********\",\"PrcTime\":\"183528\",\"DepAccFrom\":{\"AccNo\":\"**********\",\"AccShortName\":\"ทดสอบชื่อสั้น\",\"AccName\":\"ทดสอบชื่อ นามสกุล\",\"AccBalance\":\"+100000.00\",\"AccAvailableBalance\":\"+95000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"5000.00\",\"TodayHoldAmount\":\"1000.00\",\"YesterdayAmount\":\"2000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"3000.00\",\"OpenDepAmount\":\"100000.00\",\"OCCode\":\"OC1234567890\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV001\",\"BranchId\":\"BR1234567890\",\"AccType\":\"Savings\",\"BranchRegion\":\"Bangkok\",\"BranchName\":\"Chatuchak Branch\"},\"DepAccTo\":{\"AccNo\":\"**********\",\"AccShortName\":\"RECEIVER\",\"AccName\":\"RECEIVER NAME\",\"AccBalance\":\"+50000.00\",\"AccAvailableBalance\":\"+48000.00\",\"OpenDate\":\"********\",\"LastTrnDate\":\"********\",\"Note\":\"SCB Enterprise API Implementation by Enterprise Architecture Siam Commercial Bank Public Company Limited Error Handling Failure case for MQ EAPI Handle response when Bad Request such as mandatory field validation, response: HTTP 400 Unauthorized, response: HTTP401 Internal server error occurred, response: HTTP 500 Timeout: HTTP 504 Follow the backend response when MQ return \\\"<DocRs><DebitCreditVerifyRs><Status><Control/>\\\" value is \\\"1\\\" (Failed). as below\",\"OdAmount\":\"0.00\",\"HoldAmount\":\"2000.00\",\"TodayHoldAmount\":\"500.00\",\"YesterdayAmount\":\"1000.00\",\"UnclearAmount\":\"0.00\",\"TodayCashAmount\":\"1500.00\",\"OpenDepAmount\":\"50000.00\",\"OCCode\":\"OC0987654321\",\"CurrencyCode\":\"THB\",\"AccStatusCode\":\"ACT\",\"CustType\":\"INDIVIDUAL\",\"MiscMsg\":\"No issues\",\"AccProductCode\":\"SAV002\",\"BranchId\":\"BR0987654321\",\"AccType\":\"Savings\",\"BranchRegion\":\"Chiang Mai\",\"BranchName\":\"Nimman Branch\"}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TransDesc\":\"CREDITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":409,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"S0001\",\"message\":\"Error in Response\",\"severitylevel\":\"ERROR\",\"description\":\"The response returned contains an error message\",\"moreInfo\":\"ST0416: ST0416 INSUFFICIENT FUNDS\"}]}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/accounts/deposits/debitCredit/verify\"}},{\"contains\":{\"body\":{\"DebitCreditVerifyRq\":{\"HostParams\":{\"TranDesc\":\"CREDITVERIFY\"},\"DepAccFrom\":{\"AccNo\":\"**********\"},\"DepAccTo\":{\"AccNo\":\"**********\"}}}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/party/cust-profile/custinfo/findByAccount\"}},{\"contains\":{\"body\":{\"accountNumber\":\"**********\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"pagination\":{\"pagingLimit\":20,\"pagingOffset\":\"1\",\"previousPage\":\"\",\"firstPage\":\"\",\"lastPage\":\"\",\"nextPage\":\"\"},\"items\":[{\"customerType\":\"C\",\"engName\":\"MOCK FINDBYACCOUNT\",\"thaiName\":\" \",\"primaryAccountOwnerFlag\":true,\"partnerID\":\"********\",\"idNumber\":\"*************\",\"vipCode\":\"CE\",\"idTypeCode\":\"B8\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/party/cust-profile/custinfo/findByAccount\"}},{\"contains\":{\"body\":{\"accountNumber\":\"**********\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"1234\",\"originalErrorDesc\":\"Credit Card Not found\",\"description\":\"The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.\",\"message\":\"Missing juristicId query parameter\",\"moreInfo\":\"ORIGINAL ERROR: E1234 MISSING JURISTIC-ID\",\"severitylevel\":\"Error\",\"originalErrorCode\":\"RM7885\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/party/cust-profile/custinfo/findByAccount\"}},{\"contains\":{\"body\":{\"accountNumber\":\"**********\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"message\":\"mock timeout - no any return from EA\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/external-agency/detica/blacklist/validation\"}},{\"contains\":{\"body\":{\"idNumber\":\"*************\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":[{\"recordNumber\":0,\"resultCode\":\"HIT\",\"hitResults\":[{\"score\":\"100%\",\"engName\":\"Montri Karnkasem\",\"thaiName\":\"มนตรี กลั่นเกษม\",\"hitNumber\":6051,\"alertID\":\"A140500003015\",\"message\":\"Hit on AMLO Freeze04 List\",\"idNumber\":\"A140600035021\"},{\"score\":\"100%\",\"engName\":\"Montri Karnkasem\",\"thaiName\":\"มนตรี กลั่นเกษม\",\"hitNumber\":6051,\"alertID\":\"A140500003015\",\"message\":\"Hit on AMLO Freeze05 List\",\"idNumber\":\"A140600035021\"}]}]},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/external-agency/detica/blacklist/validation\"}},{\"contains\":{\"body\":{\"idNumber\":\"1234567890999\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":[{\"recordNumber\":0,\"resultCode\":\"HIT\",\"hitResults\":[{\"score\":\"100%\",\"engName\":\"Montri Karnkasem\",\"thaiName\":\"มนตรี กลั่นเกษม\",\"hitNumber\":6051,\"alertID\":\"A140500003015\",\"message\":\"Hit on AMLO High Risk List\",\"idNumber\":\"A140600035021\"}]}]},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/external-agency/detica/blacklist/validation\"}},{\"contains\":{\"body\":{\"idNumber\":\"1234567890222\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":[{\"recordNumber\":0,\"resultCode\":\"NOHIT\"}]}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/external-agency/detica/blacklist/validation\"}},{\"contains\":{\"body\":{\"idNumber\":\"1234567890113\"}}}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"1234\",\"originalErrorDesc\":\"Credit Card Not found\",\"description\":\"The juristicId query parameter is required to prevent accidentally invoking API to retrieve all data records.\",\"message\":\"Missing juristicId query parameter\",\"moreInfo\":\"ORIGINAL ERROR: E1234 MISSING JURISTIC-ID\",\"severitylevel\":\"Error\",\"originalErrorCode\":\"RM7885\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v1/external-agency/detica/blacklist/validation\"}},{\"contains\":{\"body\":{\"idNumber\":\"1234567890112\"}}}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"errors\":[{\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"severitylevel\":\"Error\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"moreInfo\":\"JMS01: Please contact the SCB API team for support.\"}]}},\"behaviors\":[{\"wait\":100}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/searchCustomerProfile\"}},{\"contains\":{\"body\":{\"query\":\"query searchCustomerProfile\"}}}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"data\":{\"searchCustomerProfile\":{\"status\":{\"code\":\"CUS-1000\",\"description\":\"Success\"},\"data\":[{\"customerKey\":\"001400000000000000000023609132\",\"vipCode\":\"\",\"birthDate\":\"\",\"nameInfo\":{\"thaiComlTitle\":\"บริษัท มหาชนจำกัด\",\"thaiComlName\":\"แอลที\",\"englishComlTitle\":\"PUBLIC COMPANY LIMITED\",\"englishComlName\":\"LT\"},\"referenceInfo\":[{\"referenceTypeCode\":\"P1\",\"referenceNumber\":\"1743889390929\"}]}],\"totalCount\":1,\"offset\":0,\"limit\":5}}}}}]},{\"predicates\":[{\"contains\":{\"path\":\"/scb/rest/ent-api\"}}],\"responses\":[{\"proxy\":{\"to\":\"https://intapigw-sit.se.scb.co.th:8448\",\"mode\":\"proxyTransparent\",\"predicateGenerators\":[{\"matches\":{\"path\":true}}]}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000001.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RQST.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DocRs\":{\"MerchantCOOPRs\":{\"Status\":{\"StatusCode\":\"0\",\"StatusDesc\":\"Success\"}}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000002.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RQST.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DocRs\":{\"MerchantCOOPRs\":{\"Status\":{\"StatusCode\":\"99\",\"StatusDesc\":\"Error\"}}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000007.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RQST.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RQST.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000005.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000009.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000010.*\"}}}}}]},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RVSL.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000005.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000007.*\"}}}}}]},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DocRs\":{\"MerchantCOOPRs\":{\"Status\":{\"StatusCode\":\"0\",\"StatusDesc\":\"Success\"}}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000005.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RVSL.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":500,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"DocRs\":{\"MerchantCOOPRs\":{\"Status\":{\"StatusCode\":\"99\",\"StatusDesc\":\"Error\"}}}}}}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000010.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RVSL.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\"}},\"behaviors\":[{\"wait\":180000}]}]},{\"predicates\":[{\"equals\":{\"method\":\"POST\",\"path\":\"/v4/coop/transaction\"}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*9990000009.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*RVSL.*\"}}}}},{\"or\":[{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*10.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*30.*\"}}}}},{\"matches\":{\"body\":{\"DocRq\":{\"MerchantCOOPRq\":{\"COOPMsg\":\".*40.*\"}}}}}]}],\"responses\":[{\"is\":{\"statusCode\":504,\"headers\":{\"Content-Type\":\"application/json\"},\"body\":{\"code\":\"JMS01\",\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"message\":\"JMS Timeout\",\"moreInfo\":\".\",\"errors\":[{\"description\":\"JMS Message timeout, No response return within limit (default 100 second).\",\"code\":\"JMS01\",\"message\":\"JMS Timeout\",\"moreInfo\":\"JMS Message timeout, Please contact the SCB API team for support.\",\"errors\":\"\",\"severitylevel\":\"ERROR\"}],\"severitylevel\":\"ERROR\"}}}]},{\"predicates\":[{\"contains\":{\"path\":\"/coop/temp\"}}],\"responses\":[{\"proxy\":{\"to\":\"https://coop:8448\",\"mode\":\"proxyTransparent\",\"predicateGenerators\":[{\"matches\":{\"path\":true}}]}}]}]}]}","timestamp":"2025-09-17T12:08:46.027Z"}
{"level":"info","message":"[http:8080 atimi-mock] Open for business...","timestamp":"2025-09-17T12:08:46.034Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56865 ESTABLISHED","timestamp":"2025-09-17T12:08:47.488Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56866 ESTABLISHED","timestamp":"2025-09-17T12:08:47.489Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56865 LAST-ACK","timestamp":"2025-09-17T12:08:47.489Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56865 CLOSED","timestamp":"2025-09-17T12:08:47.490Z"}
{"level":"info","message":"[http:8080 atimi-mock] ::1:56866 => POST /v4/coop/transaction","timestamp":"2025-09-17T12:08:47.491Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56866 => {\"requestFrom\":\"::1:56866\",\"method\":\"POST\",\"path\":\"/v4/coop/transaction\",\"query\":{},\"headers\":{\"Content-Type\":\"application/json\",\"User-Agent\":\"PostmanRuntime/7.46.1\",\"Accept\":\"*/*\",\"Cache-Control\":\"no-cache\",\"Postman-Token\":\"2a483a51-2a2e-4d95-8006-72da3a15b909\",\"Host\":\"localhost:8080\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"101\"},\"body\":\"{\\n    \\\"DocRq\\\": {\\n        \\\"MerchantCOOPRq\\\": {\\n            \\\"COOPMsg\\\": \\\"RVSLๅๅๅ\\\"\\n        }\\n    }\\n}\",\"ip\":\"::1\"}","timestamp":"2025-09-17T12:08:47.493Z"}
{"level":"info","message":"[http:8080 atimi-mock] no predicate match, using default response","timestamp":"2025-09-17T12:08:47.510Z"}
{"level":"debug","message":"[http:8080 atimi-mock] generating response from {\"is\":{}}","timestamp":"2025-09-17T12:08:47.511Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56866 <= {\"statusCode\":200,\"headers\":{\"Content-Type\":\"application/json\",\"Access-Control-Allow-Headers\":\"*\",\"Access-Control-Allow-Methods\":\"*\",\"Access-Control-Allow-Origin\":\"*\",\"Connection\":\"close\"},\"body\":\"\",\"_mode\":\"text\"}","timestamp":"2025-09-17T12:08:47.513Z"}
{"level":"debug","message":"[http:8080 atimi-mock] ::1:56866 CLOSED","timestamp":"2025-09-17T12:08:47.514Z"}
