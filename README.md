# Mock Service

A Mountebank-based mock service for API testing and development.

## Prerequisites
Install Mountebank globally:
1.npm install -g mountebank
2.brew install node

## Quick Start

### Basic Usage

Start the mock service:
# mb command
```bash
mb --configfile ./mock.ejs --allowInjection
mb --configfile ./mb-dev.ejs --allowInjection
mb --configfile ./mb-qa.ejs --allowInjection
```

The service will be available at:
- **Management Interface**: http://localhost:2525/
- **Mock API**: http://localhost:8080/

### Available Endpoints

The mock service provides the following test endpoints:

#### Template Service
- **POST** `/template` - Template service with configurable responses
  - Success response: Send `{"accountNumber": "Success"}` in request body
  - Error response: Send `{"status": "Fail201"}` in request body

## Configuration

### File Structure

```
.
├── mock.ejs                 # Main configuration entry point
├── dev/
│   ├── mb.ejs              # Development environment configuration
│   └── legacy-service/
│       ├── legacy-service.ejs
│       └── template/
│           ├── template-post-success-200.ejs
│           └── template-post-fail-201.ejs
└── README.md
```

### Mock Responses

The service includes predefined mock responses for testing various scenarios:

- **200 Success**: Returns successful response with status code 200
- **201 Error**: Returns error response with status code 201

## Process Management

### Using PM2 (Optional)

If you have PM2 installed, you can manage the service as a background process:

```bash
# Start with PM2
pm2 start pm2.config.js

# Check status
pm2 list

# Stop service
pm2 stop pm2.config.js

# Restart service
pm2 reload all

# Delete all processes
pm2 delete all

# View logs
pm2 logs
```

### Manual Process Management

Check what's running on port 8080:
```bash
lsof -i :8080
```

Kill process by PID:
```bash
kill -9 [PID]
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill existing mountebank processes
   lsof -ti:2525 | xargs kill -9
   lsof -ti:8080 | xargs kill -9
   ```

2. **Configuration file not found**
   - Ensure you're running the command from the project root directory
   - Verify the `mock.ejs` file exists

3. **Permission denied**
   - Make sure Mountebank is installed globally with proper permissions
   - Try running with `sudo` if necessary

### Debug Mode

Run with debug logging:
```bash
mb --configfile mock.ejs --allowInjection --loglevel debug
```

## Development

### Adding New Mock Endpoints

1. Create new `.ejs` files in the appropriate service directory
2. Include them in the parent service configuration
3. Restart the mock service

### Testing Mock Endpoints

Use curl to test endpoints:

```bash
# Test success response
curl -X POST http://localhost:8080/template \
  -H "Content-Type: application/json" \
  -d '{"accountNumber": "Success"}'

# Test error response
curl -X POST http://localhost:8080/template \
  -H "Content-Type: application/json" \
  -d '{"status": "Fail201"}'
```