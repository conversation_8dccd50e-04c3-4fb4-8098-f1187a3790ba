<% include ./confirm-coop/verify-coop-success.ejs %>,
<% include ./confirm-coop/verify-coop-fail-500.ejs %>,
<% include ./confirm-coop/verify-coop-timeout-no-response.ejs %>,
<% include ./confirm-coop/verify-coop-timeout-jms01.ejs %>,

<% include ./reverse-coop/reverse-coop-success.ejs %>,
<% include ./reverse-coop/reverse-coop-fail-500.ejs %>,
<% include ./reverse-coop/reverse-coop-timeout-no-response.ejs %>,
<% include ./reverse-coop/reverse-coop-timeout-jms01.ejs %>,

{
	"predicates": [
	    {
		    "contains": {
		        "path": "/coop/temp"
		    }
	    }
	],
	"responses": [
	    {
		    "proxy": {
		        "to": "https://coop:8448",
		        "mode": "proxyTransparent",
		        "predicateGenerators": [{ "matches": { "path": true } }]
		    }
	    }
	]
}

