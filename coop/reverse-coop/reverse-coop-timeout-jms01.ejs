{"predicates": [{"equals": {"method": "POST", "path": "/v4/coop/transaction"}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*9990000009.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*RVSL.*"}}}}}, {"or": [{"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*10.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*30.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*40.*"}}}}}]}], "responses": [{"is": {"statusCode": 504, "headers": {"Content-Type": "application/json"}, "body": {"code": "JMS01", "description": "JMS Message timeout, No response return within limit (default 100 second).", "message": "JMS Timeout", "moreInfo": ".", "errors": [{"description": "JMS Message timeout, No response return within limit (default 100 second).", "code": "JMS01", "message": "JMS Timeout", "moreInfo": "JMS Message timeout, Please contact the SCB API team for support.", "errors": "", "severitylevel": "ERROR"}], "severitylevel": "ERROR"}}}]}