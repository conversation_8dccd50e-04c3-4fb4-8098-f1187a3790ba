{"predicates": [{"equals": {"method": "POST", "path": "/v4/coop/transaction"}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*9990000002.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*RQST.*"}}}}}, {"or": [{"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*10.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*30.*"}}}}}, {"matches": {"body": {"DocRq": {"MerchantCOOPRq": {"COOPMsg": ".*40.*"}}}}}]}], "responses": [{"is": {"statusCode": 500, "headers": {"Content-Type": "application/json"}, "body": {"DocRs": {"MerchantCOOPRs": {"Status": {"StatusCode": "99", "StatusDesc": "Error"}}}}}}]}